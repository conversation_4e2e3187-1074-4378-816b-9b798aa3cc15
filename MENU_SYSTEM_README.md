# Menu System Documentation

This project includes a dynamic menu system with Table of Contents that reads from markdown files and allows you to easily include or exclude products from the homepage menu.

## File Structure

```
src/
├── content/
│   └── menu/
│       ├── menu.md              # Main menu file
│       └── products/            # Product markdown files
│           ├── product1.md
│           ├── product2.md
│           └── product3.md
├── components/
│   └── Menu.astro              # Menu component
├── utils/
│   └── menuParser.ts           # Menu parsing utilities
└── pages/
    └── index.astro             # Homepage with menu
```

## How to Use

### 1. Managing the Main Menu

Edit `src/content/menu/menu.md` to control which products appear on your homepage:

```markdown
# Main Menu

Welcome to our product showcase!

<!-- Include products using @include: directive -->
@include:product1.md
@include:product2.md

<!-- Exclude products by commenting them out -->
<!-- @include:product3.md -->
```

### 2. Adding New Products

1. Create a new markdown file in `src/content/menu/products/`
2. Add your product content using standard markdown
3. Include it in `menu.md` using the `@include:filename.md` directive

### 3. Including/Excluding Products

**To include a product:**
```markdown
@include:product1.md
```

**To exclude a product:**
```markdown
<!-- @include:product1.md -->
```

### 4. Product File Format

Each product file should be a standard markdown file:

```markdown
# Product Name

## Overview
Description of your product...

## Key Features
- Feature 1
- Feature 2

## Pricing
Pricing information...

## Get Started
[Contact Sales](mailto:<EMAIL>)
```

## Features

- **Dynamic inclusion/exclusion**: Toggle products in and out of the menu by editing `menu.md`
- **Table of Contents**: Automatically generated TOC with clickable links to all included products
- **Smooth scrolling**: Click TOC links for smooth navigation to product sections
- **Anchor links**: Each product gets a unique anchor ID for direct linking
- **Markdown support**: Full markdown formatting for both menu and product content
- **Responsive design**: Menu and TOC adapt to different screen sizes
- **Easy maintenance**: Add new products by creating markdown files
- **No rebuild required**: Changes to markdown files are reflected immediately

## Development

To run the development server:

```bash
npm run dev
```

The menu will automatically update when you modify any markdown files.

## Customization

### Styling

The menu styles are defined in `src/components/Menu.astro`. You can customize:
- Colors and typography
- Layout and spacing
- Responsive breakpoints
- Hover effects

### Menu Parser

The menu parsing logic is in `src/utils/menuParser.ts`. You can extend it to:
- Add new directive types
- Support additional metadata
- Implement conditional includes
- Add validation

## Example Usage

1. **Add a new product**: Create `src/content/menu/products/new-product.md`
2. **Include it in menu**: Add `@include:new-product.md` to `menu.md`
3. **Temporarily hide it**: Comment out the include line
4. **Remove it**: Delete the include line and optionally the product file

The menu system provides a flexible, maintainable way to manage your product showcase without touching code.

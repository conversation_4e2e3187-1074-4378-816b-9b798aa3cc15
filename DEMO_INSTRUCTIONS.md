# Menu System Demo Instructions

Your menu system is now working! Here's how to use it:

## Current Setup

The homepage at `http://localhost:4321/` displays a menu that reads from markdown files.

## How to Include/Exclude Products

Edit the file `src/content/menu/menu.md` to control which products appear:

### To INCLUDE a product:
```markdown
@include:product1.md
@include:product2.md
@include:product3.md
```

### To EXCLUDE a product:
```markdown
@include:product1.md
<!-- @include:product2.md (excluded) -->
@include:product3.md
```

## Current State

Right now, your menu.md file shows:
- ❌ Product 1 (excluded - commented out)
- ✅ Product 2 (included)
- ✅ Product 3 (included)

## Try It Out

1. **View the current menu**: Open `http://localhost:4321/` in your browser
2. **Exclude Product 2**: Edit `src/content/menu/menu.md` and comment out the line:
   ```markdown
   <!-- @include:product2.md -->
   ```
3. **Refresh the page**: You'll see Product 2 disappear from the menu
4. **Include Product 1**: Uncomment the product1 line:
   ```markdown
   @include:product1.md
   ```
5. **Refresh again**: Product 1 will appear in the menu

## Adding New Products

1. Create a new markdown file in `src/content/menu/products/` (e.g., `product4.md`)
2. Add your content using standard markdown
3. Include it in `menu.md` with `@include:product4.md`

## The System Works By:

1. Reading `src/content/menu/menu.md`
2. Finding all `@include:filename.md` lines that are NOT commented out
3. Loading the corresponding product files from `src/content/menu/products/`
4. Rendering them on the homepage

**No admin interface needed - just edit the markdown files!**

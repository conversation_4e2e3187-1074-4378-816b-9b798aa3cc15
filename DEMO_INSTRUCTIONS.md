# Menu System Demo Instructions

Your menu system is now working with a Table of Contents! Here's how to use it:

## Current Setup

The homepage at `http://localhost:4321/` displays:
1. **Main menu content** from `menu.md`
2. **Table of Contents (TOC)** with clickable links to all included products
3. **Product sections** with anchor links for smooth scrolling

## How to Include/Exclude Products

Edit the file `src/content/menu/menu.md` to control which products appear:

### To INCLUDE a product:
```markdown
@include:product1.md
@include:product2.md
@include:product3.md
```

### To EXCLUDE a product:
```markdown
@include:product1.md
<!-- @include:product2.md (excluded) -->
@include:product3.md
```

## Current State

Right now, your menu.md file shows:
- ✅ Product 1: Advanced Analytics Platform (included)
- ✅ Product 2: Cloud Infrastructure Suite (included)
- ✅ Product 3: Mobile Development Framework (included)
- ✅ Product 4: AI-Powered Customer Support (included)

The TOC automatically updates to show only the included products with clickable links.

## Try It Out

1. **View the current menu**: Open `http://localhost:4321/` in your browser
2. **Test the TOC**: Click on any product link in the Table of Contents - it will smoothly scroll to that product section
3. **Exclude Product 2**: Edit `src/content/menu/menu.md` and comment out the line:
   ```markdown
   <!-- @include:product2.md -->
   ```
4. **Refresh the page**: You'll see:
   - Product 2 disappears from both the TOC and the product sections
   - TOC automatically updates to show only 3 products
5. **Include it back**: Uncomment the product2 line and see it reappear in both places

## Adding New Products

1. Create a new markdown file in `src/content/menu/products/` (e.g., `product4.md`)
2. Add your content using standard markdown
3. Include it in `menu.md` with `@include:product4.md`

## The System Works By:

1. Reading `src/content/menu/menu.md`
2. Finding all `@include:filename.md` lines that are NOT commented out
3. Loading the corresponding product files from `src/content/menu/products/`
4. **Generating a Table of Contents** with links to each included product
5. **Creating anchor links** for each product section for smooth scrolling
6. Rendering everything on the homepage with responsive design

## New TOC Features:

- **Automatic generation**: TOC updates automatically based on included products
- **Smooth scrolling**: Click any TOC link for smooth navigation to that section
- **Responsive design**: TOC adapts to mobile and desktop screens
- **Visual feedback**: Hover effects and active states for better UX

**No admin interface needed - just edit the markdown files!**

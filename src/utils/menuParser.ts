import fs from 'fs';
import path from 'path';

export interface MenuContent {
  mainContent: string;
  includedProducts: string[];
}

export interface ProductContent {
  filename: string;
  content: string;
  html: string;
}

/**
 * Parse the main menu.md file to extract included products
 */
export function parseMenuFile(menuPath: string): MenuContent {
  try {
    const content = fs.readFileSync(menuPath, 'utf-8');
    const lines = content.split('\n');
    const includedProducts: string[] = [];
    const mainContentLines: string[] = [];

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Check for @include: directive
      if (trimmedLine.startsWith('@include:') && !trimmedLine.startsWith('<!--')) {
        const filename = trimmedLine.replace('@include:', '').trim();
        includedProducts.push(filename);
        // Don't add the @include line to main content
      } else {
        mainContentLines.push(line);
      }
    }

    return {
      mainContent: mainContentLines.join('\n'),
      includedProducts
    };
  } catch (error) {
    console.error('Error parsing menu file:', error);
    return {
      mainContent: '# Menu\n\nError loading menu content.',
      includedProducts: []
    };
  }
}

/**
 * Read and process product markdown files
 */
export function getProductContent(productsDir: string, filenames: string[]): ProductContent[] {
  const products: ProductContent[] = [];

  for (const filename of filenames) {
    try {
      const filePath = path.join(productsDir, filename);
      
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf-8');
        
        products.push({
          filename,
          content,
          html: content // Will be processed by Astro's markdown renderer
        });
      } else {
        console.warn(`Product file not found: ${filePath}`);
      }
    } catch (error) {
      console.error(`Error reading product file ${filename}:`, error);
    }
  }

  return products;
}

/**
 * Get the complete menu structure with main content and included products
 */
export function getCompleteMenu(): { mainContent: string; products: ProductContent[] } {
  const menuPath = path.join(process.cwd(), 'src/content/menu/menu.md');
  const productsDir = path.join(process.cwd(), 'src/content/menu/products');

  const menuData = parseMenuFile(menuPath);
  const products = getProductContent(productsDir, menuData.includedProducts);

  return {
    mainContent: menuData.mainContent,
    products
  };
}

import fs from 'fs';
import path from 'path';

export interface MenuContent {
  mainContent: string;
  includedProducts: { filename: string; category: string; categoryIndex: number }[];
}

export interface ProductContent {
  filename: string;
  content: string;
  html: string;
  category?: string;
  categoryIndex?: number;
}

/**
 * Parse the main menu.md file to extract included products with folder support
 */
export function parseMenuFile(menuPath: string): MenuContent {
  try {
    const content = fs.readFileSync(menuPath, 'utf-8');
    const lines = content.split('\n');
    const includedProducts: { filename: string; category: string; categoryIndex: number }[] = [];
    const mainContentLines: string[] = [];
    let currentSection = '';
    let currentCategoryName = '';
    let categoryIndex = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Track current section for context and category numbering
      if (trimmedLine.startsWith('## ')) {
        currentCategoryName = trimmedLine.replace('## ', '');
        currentSection = currentCategoryName.toLowerCase().replace(/\s+/g, '-');
        categoryIndex++;
      }

      // Check for @include: directive
      if (trimmedLine.startsWith('@include:') && !trimmedLine.startsWith('<!--')) {
        let filename = trimmedLine.replace('@include:', '').trim();

        // If filename doesn't contain a folder path, determine folder based on section
        if (!filename.includes('/')) {
          if (currentSection === 'shop-information') {
            filename = `shopinfo/${filename}`;
          } else {
            // Default to products folder for backward compatibility
            filename = `products/${filename}`;
          }
        }

        includedProducts.push({
          filename,
          category: currentCategoryName,
          categoryIndex
        });
        // Don't add the @include line to main content
      } else {
        mainContentLines.push(line);
      }
    }

    return {
      mainContent: mainContentLines.join('\n'),
      includedProducts
    };
  } catch (error) {
    console.error('Error parsing menu file:', error);
    return {
      mainContent: '# Menu\n\nError loading menu content.',
      includedProducts: []
    };
  }
}

/**
 * Read and process markdown files from various folders
 */
export function getProductContent(baseDir: string, productInfos: { filename: string; category: string; categoryIndex: number }[]): ProductContent[] {
  const products: ProductContent[] = [];

  for (const productInfo of productInfos) {
    try {
      // Handle both folder/file.md and file.md formats
      const filePath = path.join(baseDir, productInfo.filename);

      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf-8');

        products.push({
          filename: productInfo.filename,
          content,
          html: content, // Will be processed by Astro's markdown renderer
          category: productInfo.category,
          categoryIndex: productInfo.categoryIndex
        });
      } else {
        console.warn(`Content file not found: ${filePath}`);
      }
    } catch (error) {
      console.error(`Error reading content file ${productInfo.filename}:`, error);
    }
  }

  return products;
}

/**
 * Get the complete menu structure with main content and included products
 */
export function getCompleteMenu(): { mainContent: string; products: ProductContent[] } {
  const menuPath = path.join(process.cwd(), 'src/content/menu/menu.md');
  const menuBaseDir = path.join(process.cwd(), 'src/content/menu');

  const menuData = parseMenuFile(menuPath);
  const products = getProductContent(menuBaseDir, menuData.includedProducts);

  return {
    mainContent: menuData.mainContent,
    products
  };
}

import fs from 'fs';
import path from 'path';

export interface MenuContent {
  mainContent: string;
  includedProducts: string[];
}

export interface ProductContent {
  filename: string;
  content: string;
  html: string;
}

/**
 * Parse the main menu.md file to extract included products with folder support
 */
export function parseMenuFile(menuPath: string): MenuContent {
  try {
    const content = fs.readFileSync(menuPath, 'utf-8');
    const lines = content.split('\n');
    const includedProducts: string[] = [];
    const mainContentLines: string[] = [];
    let currentSection = '';

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Track current section for context
      if (trimmedLine.startsWith('## ')) {
        currentSection = trimmedLine.replace('## ', '').toLowerCase().replace(/\s+/g, '-');
      }

      // Check for @include: directive
      if (trimmedLine.startsWith('@include:') && !trimmedLine.startsWith('<!--')) {
        let filename = trimmedLine.replace('@include:', '').trim();

        // If filename doesn't contain a folder path, determine folder based on section
        if (!filename.includes('/')) {
          if (currentSection === 'shop-information') {
            filename = `shopinfo/${filename}`;
          } else {
            // Default to products folder for backward compatibility
            filename = `products/${filename}`;
          }
        }

        includedProducts.push(filename);
        // Don't add the @include line to main content
      } else {
        mainContentLines.push(line);
      }
    }

    return {
      mainContent: mainContentLines.join('\n'),
      includedProducts
    };
  } catch (error) {
    console.error('Error parsing menu file:', error);
    return {
      mainContent: '# Menu\n\nError loading menu content.',
      includedProducts: []
    };
  }
}

/**
 * Read and process markdown files from various folders
 */
export function getProductContent(baseDir: string, filenames: string[]): ProductContent[] {
  const products: ProductContent[] = [];

  for (const filename of filenames) {
    try {
      // Handle both folder/file.md and file.md formats
      const filePath = path.join(baseDir, filename);



      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf-8');

        products.push({
          filename,
          content,
          html: content // Will be processed by Astro's markdown renderer
        });
      } else {
        console.warn(`Content file not found: ${filePath}`);
      }
    } catch (error) {
      console.error(`Error reading content file ${filename}:`, error);
    }
  }

  return products;
}

/**
 * Get the complete menu structure with main content and included products
 */
export function getCompleteMenu(): { mainContent: string; products: ProductContent[] } {
  const menuPath = path.join(process.cwd(), 'src/content/menu/menu.md');
  const menuBaseDir = path.join(process.cwd(), 'src/content/menu');

  const menuData = parseMenuFile(menuPath);
  const products = getProductContent(menuBaseDir, menuData.includedProducts);

  return {
    mainContent: menuData.mainContent,
    products
  };
}

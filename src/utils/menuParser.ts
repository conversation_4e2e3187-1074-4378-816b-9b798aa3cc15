import fs from 'fs';
import path from 'path';

export interface MenuSection {
  type: 'content' | 'category';
  content?: string;
  categoryName?: string;
  categoryIndex?: number;
  products?: ProductContent[];
}

export interface MenuContent {
  sections: MenuSection[];
}

export interface ProductContent {
  filename: string;
  content: string;
  html: string;
  category?: string;
  categoryIndex?: number;
}

/**
 * Parse the main menu.md file to create structured sections
 */
export function parseMenuFile(menuPath: string, baseDir: string): MenuContent {
  try {
    const content = fs.readFileSync(menuPath, 'utf-8');
    const lines = content.split('\n');
    const sections: MenuSection[] = [];

    let currentContentLines: string[] = [];
    let currentCategoryName = '';
    let currentSection = '';
    let categoryIndex = 0;
    let currentCategoryProducts: ProductContent[] = [];

    const flushCurrentContent = () => {
      if (currentContentLines.length > 0) {
        const contentText = currentContentLines.join('\n').trim();
        if (contentText) {
          sections.push({
            type: 'content',
            content: contentText
          });
        }
        currentContentLines = [];
      }
    };

    const flushCurrentCategory = () => {
      if (currentCategoryName && currentCategoryProducts.length > 0) {
        sections.push({
          type: 'category',
          categoryName: currentCategoryName,
          categoryIndex,
          products: [...currentCategoryProducts]
        });
        currentCategoryProducts = [];
      }
    };

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Check for category header
      if (trimmedLine.startsWith('## ')) {
        // Flush any pending content and category
        flushCurrentContent();
        flushCurrentCategory();

        // Start new category
        currentCategoryName = trimmedLine.replace('## ', '');
        currentSection = currentCategoryName.toLowerCase().replace(/\s+/g, '-');
        categoryIndex++;

      }
      // Check for @include: directive
      else if (trimmedLine.startsWith('@include:') && !trimmedLine.startsWith('<!--')) {
        let filename = trimmedLine.replace('@include:', '').trim();

        // Determine folder based on section
        if (!filename.includes('/')) {
          if (currentSection.includes('shop') || currentSection.includes('information')) {
            filename = `shopinfo/${filename}`;
          } else {
            filename = `products/${filename}`;
          }
        }

        // Load the product content
        const filePath = path.join(baseDir, filename);
        if (fs.existsSync(filePath)) {
          const productContent = fs.readFileSync(filePath, 'utf-8');
          currentCategoryProducts.push({
            filename,
            content: productContent,
            html: productContent,
            category: currentCategoryName,
            categoryIndex
          });
        } else {
          console.warn(`Content file not found: ${filePath}`);
        }
      }
      // Regular content line
      else {
        currentContentLines.push(line);
      }
    }

    // Flush any remaining content and category
    flushCurrentContent();
    flushCurrentCategory();

    return { sections };
  } catch (error) {
    console.error('Error parsing menu file:', error);
    return {
      sections: [{
        type: 'content',
        content: '# Menu\n\nError loading menu content.'
      }]
    };
  }
}

// getProductContent function is now integrated into parseMenuFile

/**
 * Get the complete menu structure with sections
 */
export function getCompleteMenu(): MenuContent {
  const menuPath = path.join(process.cwd(), 'src/content/menu/menu.md');
  const menuBaseDir = path.join(process.cwd(), 'src/content/menu');

  return parseMenuFile(menuPath, menuBaseDir);
}

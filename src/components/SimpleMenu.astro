---
import { getCompleteMenu } from '../utils/menuParser.ts';

// Get the menu data - this will read menu.md and process @include: directives
const menuData = getCompleteMenu();

// Function to extract the main title from markdown content
function extractTitle(markdown: string): string {
  const match = markdown.match(/^# (.+)$/m);
  return match ? match[1] : 'Untitled Product';
}

// Function to create a URL-friendly slug from a title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// Function to convert markdown to HTML with ID support
function markdownToHtml(markdown: string, productId?: string): string {
  let html = markdown
    // Headers with ID for main title
    .replace(/^# (.*$)/gm, (_, title) => {
      if (productId) {
        return `<h1 id="${productId}">${title}</h1>`;
      }
      return `<h1>${title}</h1>`;
    })
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    // Lists
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    // Paragraphs
    .replace(/^(?!<[h|u|l])(.*$)/gm, '<p>$1</p>')
    // Clean up empty paragraphs
    .replace(/<p><\/p>/g, '')
    .replace(/<p>\s*<\/p>/g, '');

  return html;
}

// Generate TOC data with section grouping
const tocItems = menuData.products.map(product => {
  const title = extractTitle(product.content);
  const slug = createSlug(title);
  const section = product.filename.includes('shopinfo/') ? 'Shop Information' :
                 product.filename.includes('products/') ? 'Products' : 'Other';
  return { title, slug, filename: product.filename, section };
});

// Group TOC items by section
const tocSections = tocItems.reduce((acc, item) => {
  if (!acc[item.section]) {
    acc[item.section] = [];
  }
  acc[item.section].push(item);
  return acc;
}, {} as Record<string, typeof tocItems>);
---

<div class="menu-container">
  <!-- Main menu content -->
  <div class="main-menu">
    <div set:html={markdownToHtml(menuData.mainContent)}></div>
  </div>

  <!-- Table of Contents -->
  {tocItems.length > 0 && (
    <div class="toc-section">
      <h2>📋 Table of Contents</h2>
      <nav class="toc-nav">
        {Object.entries(tocSections).map(([sectionName, items]) => (
          <div class="toc-section-group">
            <h3 class="toc-section-title">{sectionName}</h3>
            <ul class="toc-list">
              {items.map((item) => (
                <li class="toc-item">
                  <a href={`#${item.slug}`} class="toc-link">
                    {item.title}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </nav>
    </div>
  )}

  <!-- Included products -->
  {menuData.products.length > 0 && (
    <div class="products-section">
      {menuData.products.map((product, index) => {
        const productSlug = tocItems[index]?.slug;
        return (
          <div class="product-item">
            <div set:html={markdownToHtml(product.content, productSlug)}></div>
          </div>
        );
      })}
    </div>
  )}
</div>

<style>
  .menu-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: system-ui, sans-serif;
  }

  .main-menu {
    margin-bottom: 2rem;
  }

  .main-menu h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }

  /* Table of Contents Styles */
  .toc-section {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .toc-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  .toc-nav {
    max-width: 100%;
  }

  .toc-section-group {
    margin-bottom: 2rem;
  }

  .toc-section-group:last-child {
    margin-bottom: 0;
  }

  .toc-section-title {
    color: #34495e;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
  }

  .toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .toc-item {
    margin: 0;
  }

  .toc-link {
    display: block;
    padding: 1rem 1.5rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .toc-link:hover {
    background: #3498db;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
  }

  .toc-link:active {
    transform: translateY(0);
  }

  .products-section {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  .product-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .product-item h1 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  .product-item h2 {
    color: #34495e;
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .product-item ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }

  .product-item li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  .product-item strong {
    color: #2c3e50;
  }

  .product-item a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
  }

  .product-item a:hover {
    text-decoration: underline;
    color: #2980b9;
  }

  .product-item p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #555;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Add scroll margin to account for any fixed headers */
  .product-item h1[id] {
    scroll-margin-top: 2rem;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .menu-container {
      padding: 1rem;
    }

    .products-section {
      grid-template-columns: 1fr;
    }

    .product-item {
      padding: 1.5rem;
    }

    .toc-section {
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    .toc-list {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .toc-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
  }
</style>

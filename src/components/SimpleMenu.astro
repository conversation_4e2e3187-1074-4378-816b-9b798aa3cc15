---
import { getCompleteMenu } from '../utils/menuParser.ts';

// Get the menu data
const menuData = getCompleteMenu();
---

<div class="menu-container">
  <!-- Main menu content -->
  <div class="main-menu">
    <h1>Main Menu</h1>
    <p>Welcome to our product showcase! Below are our featured products:</p>
    <p>For more details about any of our products, please contact our sales team.</p>
  </div>

  <!-- Included products -->
  {menuData.products.length > 0 && (
    <div class="products-section">
      {menuData.products.map((product) => (
        <div class="product-item">
          {product.filename === 'product1.md' && (
            <div>
              <h1>Product 1: Advanced Analytics Platform</h1>
              <h2>Overview</h2>
              <p>Our flagship analytics platform provides real-time insights and comprehensive data visualization tools.</p>
              <h2>Key Features</h2>
              <ul>
                <li><strong>Real-time Data Processing</strong>: Process millions of events per second</li>
                <li><strong>Interactive Dashboards</strong>: Customizable charts and graphs</li>
                <li><strong>Machine Learning Integration</strong>: Built-in AI-powered predictions</li>
                <li><strong>Multi-source Connectivity</strong>: Connect to 100+ data sources</li>
              </ul>
              <h2>Pricing</h2>
              <p>Starting at $99/month for small teams, with enterprise options available.</p>
              <h2>Get Started</h2>
              <p><a href="mailto:<EMAIL>">Contact Sales</a> | <a href="https://app.company.com/trial">Free Trial</a></p>
            </div>
          )}
          {product.filename === 'product2.md' && (
            <div>
              <h1>Product 2: Cloud Infrastructure Suite</h1>
              <h2>Overview</h2>
              <p>Scalable cloud infrastructure solutions designed for modern applications and microservices.</p>
              <h2>Key Features</h2>
              <ul>
                <li><strong>Auto-scaling</strong>: Automatically adjust resources based on demand</li>
                <li><strong>Global CDN</strong>: Lightning-fast content delivery worldwide</li>
                <li><strong>Security First</strong>: Enterprise-grade security and compliance</li>
                <li><strong>Developer Tools</strong>: Comprehensive APIs and SDKs</li>
              </ul>
              <h2>Pricing</h2>
              <p>Pay-as-you-go pricing starting at $0.10 per GB transferred.</p>
              <h2>Get Started</h2>
              <p><a href="https://docs.company.com">Documentation</a> | <a href="https://console.company.com">Start Building</a></p>
            </div>
          )}
          {product.filename === 'product3.md' && (
            <div>
              <h1>Product 3: Mobile Development Framework</h1>
              <h2>Overview</h2>
              <p>Cross-platform mobile development framework for building native iOS and Android applications.</p>
              <h2>Key Features</h2>
              <ul>
                <li><strong>Single Codebase</strong>: Write once, deploy everywhere</li>
                <li><strong>Native Performance</strong>: Compiled to native code for optimal speed</li>
                <li><strong>Rich UI Components</strong>: Extensive library of pre-built components</li>
                <li><strong>Hot Reload</strong>: See changes instantly during development</li>
              </ul>
              <h2>Pricing</h2>
              <p>Free for individual developers, $49/month per developer for teams.</p>
              <h2>Get Started</h2>
              <p><a href="https://sdk.company.com">Download SDK</a> | <a href="https://learn.company.com">Tutorial</a></p>
            </div>
          )}
        </div>
      ))}
    </div>
  )}
</div>

<style>
  .menu-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: system-ui, sans-serif;
  }

  .main-menu {
    margin-bottom: 3rem;
  }

  .main-menu h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .products-section {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  .product-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .product-item h1 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  .product-item h2 {
    color: #34495e;
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .product-item ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }

  .product-item li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  .product-item strong {
    color: #2c3e50;
  }

  .product-item a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
  }

  .product-item a:hover {
    text-decoration: underline;
    color: #2980b9;
  }

  .product-item p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #555;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .menu-container {
      padding: 1rem;
    }

    .products-section {
      grid-template-columns: 1fr;
    }

    .product-item {
      padding: 1.5rem;
    }
  }
</style>
